"""
SOTA可通行性分割Handler
与CarDreamer框架集成的SOTA任务感知分割模块

功能：
1. 实时RGB图像处理
2. SOTA分割网络推理
3. 特征嵌入输出给DreamerV3
4. 可视化和调试支持
"""

import numpy as np
import jax
import jax.numpy as jnp
import pickle
from pathlib import Path
from typing import Dict, Any, Optional, Tuple

# CarDreamer imports
from car_dreamer.toolkit.observer.handlers.base_handler import BaseHandler
from car_dreamer.toolkit import WorldManager

# SOTA模型imports
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
from models.sota_task_aware_seg import SOTATaskAwareSegNet


class SOTATraversabilityHandler(BaseHandler):
    """
    SOTA可通行性分割Handler
    
    集成SOTA任务感知分割网络到CarDreamer观察系统
    """
    
    def __init__(self, world: WorldManager, config: Dict[str, Any]):
        super().__init__(world, config)
        
        self.world = world
        self.config = config
        
        # 模型配置
        self.feature_dim = config.get('feature_dim', 256)
        self.task_context_dim = config.get('task_context_dim', 64)
        self.output_dim = config.get('output_dim', 128)  # 输出给DreamerV3的维度
        
        # 初始化模型
        self.setup_model()
        
        # 推理状态
        self.previous_features = None
        self.inference_count = 0
        
        # 可视化配置
        self.enable_visualization = config.get('enable_visualization', False)
        self.save_visualizations = config.get('save_visualizations', False)
        self.viz_save_dir = Path(config.get('viz_save_dir', './visualizations'))
        
        if self.save_visualizations:
            self.viz_save_dir.mkdir(exist_ok=True)
        
        print(f"🚀 SOTA可通行性Handler初始化完成")
        print(f"   特征维度: {self.feature_dim}")
        print(f"   输出维度: {self.output_dim}")
        print(f"   可视化: {self.enable_visualization}")
    
    def setup_model(self):
        """初始化SOTA分割模型"""
        # 创建模型
        self.model = SOTATaskAwareSegNet(
            feature_dim=self.feature_dim,
            task_context_dim=self.task_context_dim
        )
        
        # 加载预训练权重
        checkpoint_path = self.config.get('checkpoint_path')
        if checkpoint_path and Path(checkpoint_path).exists():
            self.load_checkpoint(checkpoint_path)
            print(f"✅ 已加载预训练模型: {checkpoint_path}")
        else:
            # 初始化随机参数
            self.init_random_params()
            print("⚠️  使用随机初始化参数")
    
    def init_random_params(self):
        """初始化随机参数"""
        rng = jax.random.PRNGKey(42)
        
        # 创建虚拟输入
        dummy_rgb = jnp.ones((1, 224, 224, 3))
        dummy_context = jnp.ones((1, self.task_context_dim))
        
        # 初始化参数
        self.params = self.model.init(
            rng, 
            dummy_rgb, 
            dummy_context,
            training=False
        )
        
        # 编译推理函数
        self.inference_fn = jax.jit(self.model.apply)
    
    def load_checkpoint(self, checkpoint_path: str):
        """加载模型检查点"""
        with open(checkpoint_path, 'rb') as f:
            checkpoint = pickle.load(f)
        
        self.params = checkpoint['params']['seg_net']  # 提取分割网络参数
        
        # 编译推理函数
        self.inference_fn = jax.jit(self.model.apply)
    
    def get_observation_space(self) -> Dict[str, Any]:
        """定义观察空间"""
        return {
            'traversability_embed': {
                'shape': (self.output_dim,),
                'dtype': np.float32,
                'description': 'SOTA任务感知可通行性特征嵌入'
            },
            'traversability_score': {
                'shape': (),
                'dtype': np.float32,
                'description': '任务感知可通行性评分'
            }
        }
    
    def get_observation(self, env_state: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        获取观察结果
        Args:
            env_state: 环境状态，包含RGB图像等信息
        Returns:
            (observations, info)
        """
        try:
            # 1. 获取RGB图像
            rgb_image = self.extract_rgb_image(env_state)
            if rgb_image is None:
                return self.get_default_observation()
            
            # 2. 构建任务上下文
            task_context = self.build_task_context(env_state)
            
            # 3. 模型推理
            seg_outputs = self.run_inference(rgb_image, task_context)
            
            # 4. 构建观察结果
            observations = {
                'traversability_embed': seg_outputs['embed_feat'][0],  # (128,)
                'traversability_score': float(seg_outputs['task_score'][0])
            }
            
            # 5. 构建信息字典
            info = {
                'passability_prob': seg_outputs['passability_prob'][0],  # 分割概率图
                'global_feat': seg_outputs['global_feat'][0],           # 全局特征
                'inference_time': self.last_inference_time,
                'inference_count': self.inference_count
            }
            
            # 6. 可视化（如果启用）
            if self.enable_visualization:
                self.visualize_results(rgb_image, seg_outputs, env_state)
            
            # 7. 更新状态
            self.previous_features = seg_outputs['embed_feat']
            self.inference_count += 1
            
            return observations, info
            
        except Exception as e:
            print(f"❌ SOTA可通行性Handler推理错误: {e}")
            return self.get_default_observation()
    
    def extract_rgb_image(self, env_state: Dict[str, Any]) -> Optional[np.ndarray]:
        """从环境状态提取RGB图像"""
        # 尝试多种可能的键名
        possible_keys = ['camera', 'rgb', 'image', 'front_camera']
        
        for key in possible_keys:
            if key in env_state:
                image = env_state[key]
                
                # 确保图像格式正确
                if isinstance(image, np.ndarray):
                    if len(image.shape) == 3 and image.shape[-1] == 3:
                        # 归一化到[0,1]
                        if image.max() > 1.0:
                            image = image.astype(np.float32) / 255.0
                        
                        # 调整尺寸到模型期望的大小
                        image = self.resize_image(image, (224, 224))
                        
                        # 添加batch维度
                        return image[None, ...]  # (1, H, W, 3)
        
        return None
    
    def resize_image(self, image: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
        """调整图像尺寸"""
        # 简化版本，实际应该使用更好的插值方法
        from scipy.ndimage import zoom
        
        h, w = image.shape[:2]
        target_h, target_w = target_size
        
        zoom_factors = (target_h / h, target_w / w, 1)
        resized = zoom(image, zoom_factors, order=1)
        
        return resized
    
    def build_task_context(self, env_state: Dict[str, Any]) -> jnp.ndarray:
        """构建任务上下文向量"""
        context_features = []
        
        # 车辆状态
        if 'vehicle_state' in env_state:
            vehicle_state = env_state['vehicle_state']
            context_features.extend([
                vehicle_state.get('speed', 0.0),
                vehicle_state.get('acceleration', 0.0),
                vehicle_state.get('steering', 0.0),
            ])
        else:
            context_features.extend([0.0, 0.0, 0.0])
        
        # 控制信号
        if 'action' in env_state:
            action = env_state['action']
            if isinstance(action, (list, np.ndarray)) and len(action) >= 3:
                context_features.extend(action[:3])  # throttle, steer, brake
            else:
                context_features.extend([0.0, 0.0, 0.0])
        else:
            context_features.extend([0.0, 0.0, 0.0])
        
        # 奖励信号
        if 'reward' in env_state:
            context_features.append(float(env_state['reward']))
        else:
            context_features.append(0.0)
        
        # 填充到目标维度
        while len(context_features) < self.task_context_dim:
            context_features.append(0.0)
        
        # 截断到目标维度
        context_features = context_features[:self.task_context_dim]
        
        return jnp.array(context_features, dtype=jnp.float32)[None, ...]  # (1, task_context_dim)
    
    def run_inference(self, rgb_image: np.ndarray, task_context: jnp.ndarray) -> Dict[str, jnp.ndarray]:
        """运行模型推理"""
        start_time = time.time()
        
        # 转换为JAX数组
        rgb_jax = jnp.array(rgb_image, dtype=jnp.float32)
        
        # 模型推理
        seg_outputs = self.inference_fn(
            self.params,
            rgb_jax,
            task_context,
            training=False
        )
        
        self.last_inference_time = time.time() - start_time
        
        return seg_outputs
    
    def get_default_observation(self) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """获取默认观察结果（错误情况下）"""
        observations = {
            'traversability_embed': np.zeros(self.output_dim, dtype=np.float32),
            'traversability_score': 0.0
        }
        
        info = {
            'error': True,
            'inference_count': self.inference_count
        }
        
        return observations, info
    
    def visualize_results(self, rgb_image: np.ndarray, seg_outputs: Dict, env_state: Dict):
        """可视化结果"""
        if not self.enable_visualization:
            return
        
        try:
            import matplotlib.pyplot as plt
            
            fig, axes = plt.subplots(1, 3, figsize=(15, 5))
            
            # 原始图像
            axes[0].imshow(rgb_image[0])
            axes[0].set_title('RGB Input')
            axes[0].axis('off')
            
            # 可通行性概率图
            prob_map = seg_outputs['passability_prob'][0, ..., 0]
            im1 = axes[1].imshow(prob_map, cmap='RdYlGn', vmin=0, vmax=1)
            axes[1].set_title(f'Passability (Score: {seg_outputs["task_score"][0]:.3f})')
            axes[1].axis('off')
            plt.colorbar(im1, ax=axes[1])
            
            # 特征可视化（PCA降维到RGB）
            global_feat = seg_outputs['global_feat'][0]
            feat_norm = global_feat / (jnp.linalg.norm(global_feat) + 1e-8)
            
            # 简化的特征可视化
            feat_rgb = jnp.abs(feat_norm[:3])  # 取前3个维度作为RGB
            feat_rgb = feat_rgb / (jnp.max(feat_rgb) + 1e-8)
            
            axes[2].imshow(feat_rgb.reshape(1, 1, 3))
            axes[2].set_title('Feature Visualization')
            axes[2].axis('off')
            
            plt.tight_layout()
            
            # 保存可视化结果
            if self.save_visualizations:
                save_path = self.viz_save_dir / f'traversability_{self.inference_count:06d}.png'
                plt.savefig(save_path, dpi=100, bbox_inches='tight')
            
            plt.show() if self.enable_visualization else plt.close()
            
        except Exception as e:
            print(f"⚠️  可视化错误: {e}")
    
    def reset(self):
        """重置Handler状态"""
        self.previous_features = None
        self.inference_count = 0
        print("🔄 SOTA可通行性Handler已重置")
    
    def close(self):
        """关闭Handler"""
        print("🔚 SOTA可通行性Handler已关闭")
