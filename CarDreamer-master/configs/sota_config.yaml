# SOTA任务感知分割训练配置
# 基于最新技术的完整配置

# 实验信息
experiment:
  name: "sota_task_aware_segmentation"
  description: "SOTA任务感知分割网络，结合想法1和想法2"
  version: "v1.0"
  tags: ["sota", "task-aware", "segmentation", "carla", "dreamerv3"]

# 模型架构配置
model:
  # 主网络配置
  segmentation:
    backbone: "efficientnet_b0"
    feature_dim: 256
    task_context_dim: 64
    output_dim: 128  # 输出给DreamerV3的维度
    use_fpn: true
    use_aspp: true
    use_task_attention: true
    
    # EfficientNet配置
    efficientnet:
      width_coefficient: 1.0
      depth_coefficient: 1.0
      dropout_rate: 0.2
      drop_connect_rate: 0.2
    
    # FPN配置
    fpn:
      feature_dim: 256
      num_levels: 4
    
    # ASPP配置
    aspp:
      out_channels: 256
      atrous_rates: [6, 12, 18]
    
    # 任务感知注意力配置
    task_attention:
      embed_dim: 256
      num_heads: 8
      dropout: 0.1

  # 伪标签生成器配置
  pseudo_label_generator:
    use_depth: true
    use_semantic: true
    use_collision: true
    use_trajectory: true
    
    # 融合权重
    fusion_weights:
      ground: 0.3
      road: 0.4
      visual: 0.1
      danger: 0.1
      reachability: 0.1
    
    # 后处理
    post_process:
      gaussian_blur: true
      kernel_size: 3
      sigma: 1.0

# 训练配置
training:
  # 基础训练参数
  epochs: 100
  batch_size: 16
  sequence_length: 10
  gradient_accumulation_steps: 4
  
  # 学习率调度
  learning_rate: 1e-4
  lr_schedule: "cosine_with_warmup"
  warmup_steps: 2000
  total_steps: 100000
  min_lr_ratio: 0.01
  
  # 优化器配置
  optimizer:
    type: "adamw"
    weight_decay: 1e-4
    beta1: 0.9
    beta2: 0.999
    eps: 1e-8
  
  # 梯度配置
  gradient:
    clip_norm: 1.0
    clip_value: null
    
  # 正则化
  regularization:
    dropout: 0.1
    label_smoothing: 0.1
    mixup_alpha: 0.2
    cutmix_alpha: 1.0

# 损失函数配置
loss:
  # 损失权重（可学习）
  learnable_weights: true
  
  # 各项损失配置
  segmentation:
    type: "focal_dice"
    focal_alpha: 0.25
    focal_gamma: 2.0
    dice_smooth: 1e-8
    
  task_aware:
    type: "ranking_consistency"
    regression_weight: 1.0
    ranking_weight: 0.5
    action_consistency_weight: 0.3
    
  contrastive:
    type: "infonce"
    temperature: 0.1
    positive_threshold: 0.7
    negative_threshold: 0.3
    
  temporal:
    type: "l2_consistency"
    feature_weight: 1.0
    prediction_weight: 1.0
    
  uncertainty:
    type: "entropy_weighted"
    entropy_weight: 1.0
    
  # 对抗损失（可选）
  adversarial:
    enabled: false
    generator_weight: 0.1
    discriminator_lr: 1e-4

# 数据配置
data:
  # 数据路径
  data_dir: "data/carla_episodes"
  cache_dir: "data/cache"
  
  # 图像配置
  image_size: [224, 224]
  normalize: true
  mean: [0.485, 0.456, 0.406]
  std: [0.229, 0.224, 0.225]
  
  # 数据增强
  augmentation:
    enabled: true
    
    # 几何变换
    random_crop: true
    random_flip: true
    random_rotation: 10  # degrees
    
    # 颜色变换
    color_jitter:
      brightness: 0.2
      contrast: 0.2
      saturation: 0.2
      hue: 0.1
    
    # 高级增强
    gaussian_blur: true
    gaussian_noise: true
    
  # 数据加载
  dataloader:
    num_workers: 4
    pin_memory: true
    prefetch_factor: 2
    persistent_workers: true

# 验证和评估配置
evaluation:
  # 验证频率
  eval_interval: 1000
  eval_steps: 100
  
  # 评估指标
  metrics:
    - "task_correlation"  # 任务感知相关性
    - "segmentation_iou"  # 分割IoU
    - "segmentation_dice" # Dice系数
    - "inference_time"    # 推理时间
    - "model_size"        # 模型大小
    
  # 可视化
  visualization:
    enabled: true
    save_interval: 500
    num_samples: 8

# 日志和监控配置
logging:
  # 基础日志
  log_level: "INFO"
  log_interval: 100
  
  # TensorBoard
  tensorboard:
    enabled: true
    log_dir: "logs/tensorboard"
    
  # Weights & Biases
  wandb:
    enabled: false
    project: "sota-task-aware-seg"
    entity: null
    
  # 监控指标
  metrics_to_log:
    - "loss/total"
    - "loss/segmentation"
    - "loss/task_aware"
    - "loss/contrastive"
    - "loss/temporal"
    - "metrics/correlation"
    - "metrics/iou"
    - "learning_rate"
    - "gradient_norm"

# 检查点和保存配置
checkpoint:
  # 保存配置
  save_interval: 10  # epochs
  save_top_k: 3
  monitor_metric: "task_correlation"
  mode: "max"
  
  # 检查点目录
  checkpoint_dir: "checkpoints/sota"
  
  # 早停
  early_stopping:
    enabled: true
    patience: 20
    min_delta: 0.001

# 硬件和性能配置
hardware:
  # 设备配置
  device: "gpu"
  precision: "float32"  # float16, bfloat16, float32
  
  # 编译配置
  jit_compile: true
  xla_optimize: true
  
  # 内存配置
  memory_fraction: 0.9
  allow_growth: true

# 高级功能配置
advanced:
  # 模型蒸馏
  distillation:
    enabled: false
    teacher_checkpoint: null
    temperature: 4.0
    alpha: 0.7
    
  # 量化
  quantization:
    enabled: false
    method: "dynamic"  # dynamic, static, qat
    
  # 模型剪枝
  pruning:
    enabled: false
    sparsity: 0.5
    
  # 神经架构搜索
  nas:
    enabled: false
    search_space: "efficientnet"

# 调试配置
debug:
  enabled: false
  
  # 梯度检查
  gradient_checking: false
  
  # 内存分析
  memory_profiling: false
  
  # 数值稳定性
  check_numerics: false
  
  # 可视化调试
  visualize_gradients: false
  visualize_activations: false

# 部署配置
deployment:
  # 模型导出
  export_format: "jax"  # jax, onnx, tflite
  
  # 优化
  optimize_for_inference: true
  
  # 服务配置
  serving:
    batch_size: 1
    max_latency_ms: 50
    
# 随机种子
seed: 42

# 环境变量
environment:
  CUDA_VISIBLE_DEVICES: "0"
  JAX_PLATFORM_NAME: "gpu"
  XLA_PYTHON_CLIENT_PREALLOCATE: "false"
