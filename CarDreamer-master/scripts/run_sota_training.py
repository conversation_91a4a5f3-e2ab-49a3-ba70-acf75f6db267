#!/usr/bin/env python3
"""
SOTA任务感知分割训练脚本
完整的端到端训练流程

使用方法:
python scripts/run_sota_training.py --config configs/sota_config.yaml
"""

import os
import sys
import argparse
import yaml
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from tools.sota_task_aware_trainer import SOTATaskAwareTrainer, create_sota_config
from tools.carla_data_processor import CarlaDataProcessor


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='SOTA任务感知分割训练')
    
    parser.add_argument('--config', type=str, default='configs/sota_config.yaml',
                       help='配置文件路径')
    parser.add_argument('--data-dir', type=str, default='data/carla_episodes',
                       help='数据目录')
    parser.add_argument('--output-dir', type=str, default='./training_output_sota',
                       help='输出目录')
    parser.add_argument('--resume', type=str, default=None,
                       help='恢复训练的检查点路径')
    parser.add_argument('--epochs', type=int, default=100,
                       help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=16,
                       help='批次大小')
    parser.add_argument('--learning-rate', type=float, default=1e-4,
                       help='学习率')
    parser.add_argument('--device', type=str, default='gpu',
                       choices=['cpu', 'gpu'], help='设备类型')
    parser.add_argument('--debug', action='store_true',
                       help='调试模式')
    parser.add_argument('--fast-test', action='store_true',
                       help='快速测试模式')
    
    return parser.parse_args()


def load_config(config_path: str) -> dict:
    """加载配置文件"""
    if Path(config_path).exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print(f"✅ 已加载配置文件: {config_path}")
    else:
        print(f"⚠️  配置文件不存在，使用默认配置: {config_path}")
        config = create_sota_config()
    
    return config


def setup_data_processor(config: dict, data_dir: str) -> CarlaDataProcessor:
    """设置数据处理器"""
    processor_config = {
        'data_dir': data_dir,
        'batch_size': config.get('batch_size', 16),
        'sequence_length': config.get('sequence_length', 10),
        'image_size': config.get('image_size', (224, 224)),
        'augmentation': config.get('data_augmentation', {}),
        'cache_size': config.get('cache_size', 1000),
    }
    
    processor = CarlaDataProcessor(processor_config)
    return processor


def create_synthetic_data(batch_size: int = 16, num_batches: int = 100):
    """创建合成数据用于测试"""
    print("🔧 创建合成测试数据...")
    
    import jax.numpy as jnp
    import numpy as np
    
    synthetic_data = []
    
    for i in range(num_batches):
        batch = {
            'rgb_images': np.random.rand(batch_size, 224, 224, 3).astype(np.float32),
            'depth_maps': np.random.rand(batch_size, 224, 224, 1).astype(np.float32) * 50,
            'semantic_maps': np.random.randint(0, 20, (batch_size, 224, 224, 1)).astype(np.float32),
            'collision_info': np.random.rand(batch_size).astype(np.float32),
            'vehicle_state': np.random.rand(batch_size, 10).astype(np.float32),
            'rewards': np.random.randn(batch_size).astype(np.float32),
            'actions': np.random.rand(batch_size, 3).astype(np.float32),
            'task_context': np.random.rand(batch_size, 64).astype(np.float32),
        }
        synthetic_data.append(batch)
    
    print(f"✅ 已创建 {num_batches} 个合成数据批次")
    return synthetic_data


def main():
    """主函数"""
    args = parse_args()
    
    print("🚀 SOTA任务感知分割训练开始")
    print(f"   配置文件: {args.config}")
    print(f"   数据目录: {args.data_dir}")
    print(f"   输出目录: {args.output_dir}")
    print(f"   设备: {args.device}")
    print(f"   调试模式: {args.debug}")
    print(f"   快速测试: {args.fast_test}")
    
    # 1. 加载配置
    config = load_config(args.config)
    
    # 更新配置
    config.update({
        'batch_size': args.batch_size,
        'learning_rate': args.learning_rate,
        'epochs': args.epochs,
        'output_dir': args.output_dir,
        'debug': args.debug,
        'device': args.device,
    })
    
    # 快速测试模式
    if args.fast_test:
        config.update({
            'epochs': 2,
            'total_steps': 200,
            'log_interval': 10,
            'eval_interval': 50,
        })
    
    # 2. 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # 保存配置
    with open(output_dir / 'config.yaml', 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    # 3. 初始化训练器
    trainer = SOTATaskAwareTrainer(config)
    
    # 4. 恢复训练（如果指定）
    if args.resume:
        trainer.load_checkpoint(args.resume)
        print(f"📂 已恢复训练: {args.resume}")
    
    # 5. 准备数据
    if Path(args.data_dir).exists() and not args.fast_test:
        # 使用真实数据
        data_processor = setup_data_processor(config, args.data_dir)
        dataloader = data_processor.create_dataloader()
        print(f"✅ 已加载真实数据: {args.data_dir}")
    else:
        # 使用合成数据
        num_batches = 200 if not args.fast_test else 20
        dataloader = create_synthetic_data(args.batch_size, num_batches)
        print("⚠️  使用合成数据进行训练")
    
    # 6. 开始训练
    print("\n🎯 开始训练...")
    start_time = time.time()
    
    try:
        for epoch in range(args.epochs):
            print(f"\n📅 Epoch {epoch + 1}/{args.epochs}")
            
            # 训练一个epoch
            epoch_results = trainer.train_epoch(dataloader, epoch)
            
            # 输出epoch结果
            print(f"   平均损失: {epoch_results['avg_loss']:.4f}")
            print(f"   平均指标: {epoch_results['avg_metrics']}")
            
            # 保存检查点
            if (epoch + 1) % config.get('save_interval', 10) == 0:
                checkpoint_name = f'checkpoint_epoch_{epoch + 1}.pkl'
                trainer.save_checkpoint(checkpoint_name)
            
            # 早停检查
            if trainer.should_early_stop():
                print("🛑 早停触发，训练结束")
                break
    
    except KeyboardInterrupt:
        print("\n⏹️  训练被用户中断")
    
    except Exception as e:
        print(f"\n❌ 训练过程中发生错误: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
    
    finally:
        # 7. 保存最终模型
        trainer.save_checkpoint('final_model.pkl')
        
        # 8. 生成训练报告
        training_time = time.time() - start_time
        
        report = {
            'training_time': training_time,
            'total_steps': trainer.step,
            'final_epoch': trainer.epoch,
            'best_performance': trainer.best_performance,
            'config': config,
        }
        
        with open(output_dir / 'training_report.yaml', 'w') as f:
            yaml.dump(report, f, default_flow_style=False)
        
        print(f"\n🎉 训练完成!")
        print(f"   总时间: {training_time:.2f}秒")
        print(f"   总步数: {trainer.step}")
        print(f"   最佳性能: {trainer.best_performance:.4f}")
        print(f"   输出目录: {output_dir}")


if __name__ == '__main__':
    main()
