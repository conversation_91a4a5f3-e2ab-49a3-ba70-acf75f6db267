"""
SOTA伪标签生成器和损失函数
基于CARLA多模态信息生成高质量可通行性伪标签
"""

import jax
import jax.numpy as jnp
import flax.linen as nn
from typing import Dict, List, Tuple, Optional, Any
import numpy as np

f32 = jnp.float32


class PseudoLabelGenerator(nn.Module):
    """
    SOTA伪标签生成器
    基于CARLA环境的多模态信息生成可通行性伪标签
    
    输入信息：
    1. RGB图像
    2. 深度图
    3. 语义分割图  
    4. 碰撞检测
    5. 车辆状态
    6. 历史轨迹
    """
    
    @nn.compact
    def __call__(self, 
                 rgb_image,      # (B, H, W, 3) RGB图像
                 depth_map,      # (B, H, W, 1) 深度图
                 semantic_map,   # (B, H, W, 1) 语义分割图
                 collision_info, # (B,) 碰撞信息
                 vehicle_state,  # (B, state_dim) 车辆状态
                 trajectory_history=None):  # (B, T, 3) 历史轨迹
        """
        基于多模态信息生成高质量伪标签
        """
        B, H, W, _ = rgb_image.shape
        
        # 1. 基于深度的地面可通行性分析
        ground_mask = self._analyze_ground_traversability(depth_map)
        
        # 2. 基于语义的道路区域检测
        road_mask = self._extract_road_regions(semantic_map)
        
        # 3. 基于RGB的视觉线索
        visual_cues = self._extract_visual_cues(rgb_image)
        
        # 4. 基于碰撞历史的危险区域标记
        danger_mask = self._mark_danger_zones(collision_info, vehicle_state)
        
        # 5. 基于轨迹的可达性分析
        if trajectory_history is not None:
            reachability_mask = self._analyze_reachability(trajectory_history, H, W)
        else:
            reachability_mask = jnp.ones((B, H, W, 1))
        
        # 6. 多模态信息融合
        pseudo_labels = self._fuse_multimodal_cues(
            ground_mask, road_mask, visual_cues, 
            danger_mask, reachability_mask
        )
        
        # 7. 后处理：平滑和置信度估计
        pseudo_labels, confidence = self._post_process_labels(pseudo_labels)
        
        return {
            'pseudo_labels': pseudo_labels,      # (B, H, W, 1)
            'confidence': confidence,            # (B, H, W, 1)
            'ground_mask': ground_mask,
            'road_mask': road_mask,
            'danger_mask': danger_mask,
        }
    
    def _analyze_ground_traversability(self, depth_map):
        """基于深度图分析地面可通行性"""
        # 计算深度梯度
        grad_x = jnp.abs(depth_map[:, :, 1:] - depth_map[:, :, :-1])
        grad_y = jnp.abs(depth_map[:, 1:, :] - depth_map[:, :-1, :])
        
        # 填充到原始尺寸
        grad_x = jnp.pad(grad_x, ((0,0), (0,0), (0,1), (0,0)))
        grad_y = jnp.pad(grad_y, ((0,0), (0,1), (0,0), (0,0)))
        
        # 地面平坦度：梯度小的区域更可能是可通行地面
        gradient_magnitude = jnp.sqrt(grad_x**2 + grad_y**2)
        ground_smoothness = jnp.exp(-gradient_magnitude * 10.0)
        
        # 深度合理性：过近或过远的区域不可通行
        depth_validity = jnp.where(
            (depth_map > 0.5) & (depth_map < 50.0),
            1.0, 0.1
        )
        
        ground_mask = ground_smoothness * depth_validity
        return ground_mask
    
    def _extract_road_regions(self, semantic_map):
        """基于语义分割提取道路区域"""
        # CARLA语义标签：
        # 1: Road, 2: Sidewalk, 3: Building, 4: Wall, 5: Fence,
        # 6: Pole, 7: TrafficLight, 8: TrafficSign, 9: Vegetation,
        # 10: Terrain, 11: Sky, 12: Person, 13: Rider, 14: Car, etc.
        
        road_regions = (semantic_map == 1).astype(f32)           # 道路
        sidewalk_regions = (semantic_map == 2).astype(f32)       # 人行道
        terrain_regions = (semantic_map == 10).astype(f32)       # 地形
        
        # 组合可通行区域
        road_mask = (
            1.0 * road_regions +           # 道路完全可通行
            0.7 * sidewalk_regions +       # 人行道部分可通行
            0.3 * terrain_regions          # 地形谨慎可通行
        )
        
        # 明确的障碍物
        obstacles = (
            (semantic_map == 3) |   # 建筑
            (semantic_map == 4) |   # 墙
            (semantic_map == 5) |   # 围栏
            (semantic_map == 14)    # 车辆
        ).astype(f32)
        
        road_mask = road_mask * (1.0 - obstacles)
        return jnp.clip(road_mask, 0.0, 1.0)
    
    def _extract_visual_cues(self, rgb_image):
        """基于RGB图像提取视觉线索"""
        # 简化的视觉线索提取
        # 在实际应用中，这里可以使用预训练的特征提取器
        
        # 颜色统计：道路通常是灰色调
        gray = jnp.mean(rgb_image, axis=-1, keepdims=True)
        
        # 纹理分析：道路纹理相对均匀
        # 使用简单的局部方差作为纹理指标
        kernel_size = 5
        padding = kernel_size // 2
        
        # 计算局部方差（简化版本）
        mean_filter = jnp.ones((kernel_size, kernel_size, 1, 1)) / (kernel_size**2)
        local_mean = jax.lax.conv_general_dilated(
            gray, mean_filter, (1, 1), 'SAME'
        )
        local_var = jax.lax.conv_general_dilated(
            (gray - local_mean)**2, mean_filter, (1, 1), 'SAME'
        )
        
        # 低方差区域更可能是道路
        texture_cue = jnp.exp(-local_var * 5.0)
        
        return texture_cue
    
    def _mark_danger_zones(self, collision_info, vehicle_state):
        """基于碰撞历史标记危险区域"""
        B = collision_info.shape[0]
        
        # 如果发生碰撞，在车辆前方标记危险区域
        danger_intensity = jnp.where(collision_info > 0, 0.1, 1.0)
        
        # 扩展到空间维度（简化版本）
        danger_mask = danger_intensity[:, None, None, None]
        
        return danger_mask
    
    def _analyze_reachability(self, trajectory_history, H, W):
        """基于历史轨迹分析可达性"""
        B, T, _ = trajectory_history.shape
        
        # 简化版本：基于轨迹密度估计可达性
        # 实际应用中可以使用更复杂的轨迹分析
        
        # 计算轨迹变化
        traj_velocity = jnp.diff(trajectory_history, axis=1)
        avg_velocity = jnp.mean(jnp.linalg.norm(traj_velocity, axis=-1), axis=1)
        
        # 高速度区域更可能是可通行的
        reachability = jnp.clip(avg_velocity / 10.0, 0.0, 1.0)
        reachability_mask = reachability[:, None, None, None]
        
        return reachability_mask
    
    def _fuse_multimodal_cues(self, ground_mask, road_mask, visual_cues, 
                             danger_mask, reachability_mask):
        """多模态信息融合"""
        # 加权融合不同模态的信息
        weights = {
            'ground': 0.3,
            'road': 0.4,
            'visual': 0.1,
            'danger': 0.1,
            'reachability': 0.1
        }
        
        fused_labels = (
            weights['ground'] * ground_mask +
            weights['road'] * road_mask +
            weights['visual'] * visual_cues +
            weights['danger'] * danger_mask +
            weights['reachability'] * reachability_mask
        )
        
        return jnp.clip(fused_labels, 0.0, 1.0)
    
    def _post_process_labels(self, pseudo_labels):
        """后处理：平滑和置信度估计"""
        # 高斯平滑
        kernel_size = 3
        sigma = 1.0
        
        # 简化的高斯核
        kernel = jnp.ones((kernel_size, kernel_size, 1, 1)) / (kernel_size**2)
        
        smoothed_labels = jax.lax.conv_general_dilated(
            pseudo_labels, kernel, (1, 1), 'SAME'
        )
        
        # 置信度估计：基于多模态一致性
        # 这里简化为标签的方差
        confidence = 1.0 - jnp.abs(pseudo_labels - 0.5) * 2.0
        confidence = jnp.clip(confidence, 0.1, 1.0)
        
        return smoothed_labels, confidence


class AdaptivePseudoLabelRefinement(nn.Module):
    """自适应伪标签精化模块"""
    
    @nn.compact
    def __call__(self, 
                 initial_labels,    # (B, H, W, 1) 初始伪标签
                 model_predictions, # (B, H, W, 1) 模型预测
                 confidence,        # (B, H, W, 1) 置信度
                 task_feedback):    # (B,) 任务反馈
        """
        基于模型预测和任务反馈自适应精化伪标签
        """
        # 1. 基于置信度的标签更新
        high_conf_mask = (confidence > 0.8).astype(f32)
        low_conf_mask = (confidence < 0.3).astype(f32)
        
        # 2. 基于任务反馈的全局调整
        task_success = (task_feedback > 0).astype(f32)[:, None, None, None]
        
        # 成功的任务：增强当前预测的权重
        # 失败的任务：保持原始伪标签
        adaptation_weight = 0.3 * task_success + 0.1 * (1 - task_success)
        
        # 3. 精化后的伪标签
        refined_labels = (
            high_conf_mask * initial_labels +  # 高置信度保持原标签
            low_conf_mask * (
                (1 - adaptation_weight) * initial_labels +
                adaptation_weight * model_predictions
            ) +  # 低置信度融合预测
            (1 - high_conf_mask - low_conf_mask) * (
                0.7 * initial_labels + 0.3 * model_predictions
            )  # 中等置信度适度融合
        )
        
        return jnp.clip(refined_labels, 0.0, 1.0)
