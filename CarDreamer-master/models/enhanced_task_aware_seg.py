"""
Enhanced Task-aware Segmentation Network
增强的任务感知分割网络，集成卷积层和注意力机制
"""
import jax
import jax.numpy as jnp
from jax import random
import sys
import os

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'dreamerv3'))
from dreamerv3 import ninjax as nj

f32 = jnp.float32


class AttentionModule(nj.Module):
    """注意力机制模块"""
    
    def __init__(self, channels, reduction=8, name='attention'):
        self.channels = channels
        self.reduction = reduction
    
    def __call__(self, x):
        """
        Channel attention mechanism
        Args:
            x: Input tensor (B, H, W, C)
        Returns:
            Attention-weighted features
        """
        # Global average pooling
        gap = jnp.mean(x, axis=(1, 2), keepdims=True)  # (B, 1, 1, C)
        
        # Channel attention
        reduced_channels = max(1, self.channels // self.reduction)
        
        # FC layers for attention
        w1 = jnp.ones((self.channels, reduced_channels)) * 0.1
        b1 = jnp.zeros(reduced_channels)
        w2 = jnp.ones((reduced_channels, self.channels)) * 0.1
        b2 = jnp.zeros(self.channels)
        
        # Attention computation
        att = jnp.dot(gap.reshape(-1, self.channels), w1) + b1
        att = jax.nn.relu(att)
        att = jnp.dot(att, w2) + b2
        att = jax.nn.sigmoid(att)
        
        # Apply attention
        att = att.reshape(-1, 1, 1, self.channels)
        return x * att


class ConvBlock(nj.Module):
    """卷积块"""
    
    def __init__(self, out_channels, kernel_size=3, stride=1, use_attention=False, name='conv_block'):
        self.out_channels = out_channels
        self.kernel_size = kernel_size
        self.stride = stride
        self.use_attention = use_attention
        if use_attention:
            self.attention = AttentionModule(out_channels, name=f'{name}_att')
    
    def __call__(self, x):
        """
        Convolutional block with optional attention
        Args:
            x: Input tensor (B, H, W, C_in)
        Returns:
            Output tensor (B, H', W', C_out)
        """
        # 简化的卷积操作（实际应该使用真正的卷积）
        # 这里用平均池化 + 线性变换模拟
        if self.stride > 1:
            # 下采样
            h_new = x.shape[1] // self.stride
            w_new = x.shape[2] // self.stride
            x = jax.image.resize(x, (x.shape[0], h_new, w_new, x.shape[3]), method='bilinear')
        
        # 模拟卷积：全连接变换
        in_channels = x.shape[-1]
        w = jnp.ones((in_channels, self.out_channels)) * 0.1
        b = jnp.zeros(self.out_channels)
        
        # 应用变换
        x_flat = x.reshape(-1, in_channels)
        out_flat = jnp.dot(x_flat, w) + b
        out = out_flat.reshape(x.shape[0], x.shape[1], x.shape[2], self.out_channels)
        
        # 激活函数
        out = jax.nn.relu(out)
        
        # 注意力机制
        if self.use_attention:
            out = self.attention(out)
        
        return out


class EnhancedTaskAwareSegNet(nj.Module):
    """
    增强的任务感知分割网络 - 二分类可通行性版本

    特性：
    1. 二分类可通行性分割（可通行 vs 不可通行）
    2. 完全自监督学习，无需人工标注
    3. 基于驾驶行为和任务反馈的伪标签生成
    4. 任务感知优化，直接从驾驶成功率学习
    """

    def __init__(self, feature_channels=64, downsample_ratio=8, use_attention=True,
                 binary_classification=True, name='enhanced_seg'):
        self.feature_channels = feature_channels
        self.downsample_ratio = downsample_ratio
        self.use_attention = use_attention
        self.binary_classification = binary_classification  # 新增：二分类标志

        # 编码器层
        self.conv1 = ConvBlock(32, stride=2, use_attention=False, name='conv1')
        self.conv2 = ConvBlock(64, stride=2, use_attention=use_attention, name='conv2')
        self.conv3 = ConvBlock(feature_channels, stride=2, use_attention=use_attention, name='conv3')

        # 解码器层
        self.deconv1 = ConvBlock(32, stride=1, use_attention=use_attention, name='deconv1')
        self.deconv2 = ConvBlock(16, stride=1, use_attention=False, name='deconv2')

        # 输出头 - 针对二分类优化
        if binary_classification:
            # 二分类：可通行性概率图
            self.passability_head = ConvBlock(1, stride=1, use_attention=False, name='passability_head')
        else:
            # 原始多输出
            self.prob_head = ConvBlock(1, stride=1, use_attention=False, name='prob_head')

        self.feat_head = ConvBlock(feature_channels, stride=1, use_attention=False, name='feat_head')

        # 全局特征提取
        self.global_pool = lambda x: jnp.mean(x, axis=(1, 2))
        
    def __call__(self, x, driving_context=None):
        """
        前向传播 - 支持二分类可通行性分割
        Args:
            x: Input images (B, H, W, 3)
            driving_context: 驾驶上下文信息（速度、转向、奖励等）
        Returns:
            Dict with passability_prob, feat, score, multi_scale_feats
        """
        batch_size = x.shape[0]

        # 编码器
        feat1 = self.conv1(x)      # (B, H/2, W/2, 32)
        feat2 = self.conv2(feat1)  # (B, H/4, W/4, 64)
        feat3 = self.conv3(feat2)  # (B, H/8, W/8, feature_channels)

        # 解码器
        up1 = self.deconv1(feat3)  # (B, H/8, W/8, 32)
        up2 = self.deconv2(up1)    # (B, H/8, W/8, 16)

        # 输出头 - 二分类可通行性
        if self.binary_classification:
            passability_out = self.passability_head(up2)  # (B, H/8, W/8, 1)
            passability_prob = jax.nn.sigmoid(passability_out)  # 可通行性概率
            prob = passability_prob  # 向后兼容
        else:
            prob_out = self.prob_head(up2)  # (B, H/8, W/8, 1)
            prob = jax.nn.sigmoid(prob_out)
            passability_prob = prob

        feat_out = self.feat_head(up2)  # (B, H/8, W/8, feature_channels)
        feat = feat_out

        # 全局评分 - 基于任务感知
        global_feat = self.global_pool(feat3)  # (B, feature_channels)

        # 任务感知评分：结合全局特征和驾驶上下文
        if driving_context is not None:
            # 融合驾驶上下文信息
            context_weight = 0.3
            context_feat = jnp.mean(driving_context, axis=-1) if driving_context.ndim > 1 else driving_context
            score = jnp.mean(global_feat, axis=-1) + context_weight * context_feat
        else:
            score = jnp.mean(global_feat, axis=-1)  # (B,)

        # 多尺度特征（用于更丰富的表示）
        multi_scale_feats = {
            'scale_1': feat1,  # 1/2 scale
            'scale_2': feat2,  # 1/4 scale
            'scale_3': feat3,  # 1/8 scale
        }

        return {
            'passability_prob': passability_prob,  # 主要输出：可通行性概率
            'prob': prob,  # 向后兼容
            'feat': feat,
            'score': score,  # 任务感知评分
            'global_feat': global_feat,
            'multi_scale_feats': multi_scale_feats,
        }


class TaskAwareLossModule(nj.Module):
    """任务感知损失模块"""
    
    def __init__(self, config, name='task_aware_loss'):
        self.config = config
        
        # 损失权重
        self.reward_feedback_weight = getattr(config, 'reward_feedback_weight', 2.0)
        self.seg_pred_weight = getattr(config, 'seg_pred_weight', 1.0)
        self.temporal_consistency_weight = getattr(config, 'temporal_consistency_weight', 0.5)
        self.entropy_penalty_weight = getattr(config, 'entropy_penalty_weight', 0.01)
        self.multi_scale_weight = getattr(config, 'multi_scale_weight', 0.3)
        self.attention_reg_weight = getattr(config, 'attention_reg_weight', 0.1)
    
    def reward_feedback_loss(self, seg_output, rewards):
        """奖励反馈损失：分割评分应该与奖励相关"""
        score = seg_output['score']
        
        # 基础奖励反馈损失
        basic_loss = jnp.mean((score - rewards) ** 2)
        
        # 相关性损失：鼓励正相关
        score_norm = (score - jnp.mean(score)) / (jnp.std(score) + 1e-8)
        reward_norm = (rewards - jnp.mean(rewards)) / (jnp.std(rewards) + 1e-8)
        correlation_loss = -jnp.mean(score_norm * reward_norm)
        
        return basic_loss + 0.5 * correlation_loss
    
    def seg_prediction_loss(self, seg_features, next_seg_features):
        """分割预测损失：预测下一帧的分割特征"""
        if seg_features.shape[0] <= 1:
            return 0.0
        
        pred_loss = jnp.mean((seg_features[:-1] - next_seg_features[1:]) ** 2)
        return pred_loss
    
    def temporal_consistency_loss(self, seg_features):
        """时序一致性损失：相邻帧的分割特征应该相似"""
        if seg_features.shape[0] <= 1:
            return 0.0
        
        temporal_loss = jnp.mean((seg_features[:-1] - seg_features[1:]) ** 2)
        return temporal_loss
    
    def entropy_penalty(self, seg_prob):
        """熵惩罚：防止分割网络退化"""
        eps = 1e-8
        entropy = -jnp.mean(seg_prob * jnp.log(seg_prob + eps) + 
                           (1 - seg_prob) * jnp.log(1 - seg_prob + eps))
        return -entropy  # 负熵作为惩罚
    
    def multi_scale_consistency_loss(self, multi_scale_feats):
        """多尺度一致性损失"""
        scales = list(multi_scale_feats.keys())
        if len(scales) < 2:
            return 0.0
        
        consistency_loss = 0.0
        for i in range(len(scales) - 1):
            feat1 = multi_scale_feats[scales[i]]
            feat2 = multi_scale_feats[scales[i + 1]]
            
            # 将特征调整到相同尺寸
            feat1_pooled = jnp.mean(feat1, axis=(1, 2))  # Global pool
            feat2_pooled = jnp.mean(feat2, axis=(1, 2))  # Global pool
            
            # 计算一致性损失
            consistency_loss += jnp.mean((feat1_pooled - feat2_pooled) ** 2)
        
        return consistency_loss / (len(scales) - 1)
    
    def attention_regularization_loss(self, seg_output):
        """注意力正则化损失：鼓励注意力的稀疏性"""
        # 这里简化为全局特征的L1正则化
        global_feat = seg_output['global_feat']
        return jnp.mean(jnp.abs(global_feat))
    
    def compute_total_loss(self, seg_output, rewards, next_seg_output=None):
        """计算总损失"""
        losses = {}
        
        # 1. 奖励反馈损失
        losses['reward_feedback'] = self.reward_feedback_loss(seg_output, rewards)
        
        # 2. 分割预测损失
        if next_seg_output is not None:
            losses['seg_prediction'] = self.seg_prediction_loss(
                seg_output['feat'], next_seg_output['feat']
            )
        else:
            losses['seg_prediction'] = 0.0
        
        # 3. 时序一致性损失
        losses['temporal_consistency'] = self.temporal_consistency_loss(seg_output['feat'])
        
        # 4. 熵惩罚
        losses['entropy_penalty'] = self.entropy_penalty(seg_output['prob'])
        
        # 5. 多尺度一致性损失
        losses['multi_scale_consistency'] = self.multi_scale_consistency_loss(
            seg_output['multi_scale_feats']
        )
        
        # 6. 注意力正则化损失
        losses['attention_regularization'] = self.attention_regularization_loss(seg_output)
        
        # 组合总损失
        total_loss = (
            self.reward_feedback_weight * losses['reward_feedback'] +
            self.seg_pred_weight * losses['seg_prediction'] +
            self.temporal_consistency_weight * losses['temporal_consistency'] +
            self.entropy_penalty_weight * losses['entropy_penalty'] +
            self.multi_scale_weight * losses['multi_scale_consistency'] +
            self.attention_reg_weight * losses['attention_regularization']
        )
        
        # 添加总损失
        losses['total'] = total_loss
        
        return total_loss, losses


def create_enhanced_seg_config():
    """创建增强分割网络的配置"""
    config = {
        # 网络架构
        'feature_channels': 64,
        'downsample_ratio': 8,
        'use_attention': True,
        
        # 损失权重
        'reward_feedback_weight': 2.0,
        'seg_pred_weight': 1.0,
        'temporal_consistency_weight': 0.5,
        'entropy_penalty_weight': 0.01,
        'multi_scale_weight': 0.3,
        'attention_reg_weight': 0.1,
        
        # 训练参数
        'learning_rate': 1e-4,
        'gradient_clip_norm': 1.0,
        'warmup_steps': 1000,
    }
    
    return config
