"""
SOTA Task-aware Segmentation Network
基于最新技术的任务感知分割网络

技术栈：
- EfficientNet-B0 作为backbone encoder
- FPN (Feature Pyramid Network) 多尺度特征融合
- ASPP (Atrous Spatial Pyramid Pooling) 空洞卷积
- SE-Net (Squeeze-and-Excitation) 注意力机制
- Task-aware Cross-attention 任务感知交叉注意力
- Differentiable rendering for pseudo-label generation
"""

import jax
import jax.numpy as jnp
from jax import random
import flax.linen as nn
from typing import Dict, List, Tuple, Optional, Any
import numpy as np

# 添加项目路径
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from dreamerv3 import ninjax as nj

f32 = jnp.float32


class SEBlock(nn.Module):
    """Squeeze-and-Excitation Block - SOTA注意力机制"""
    reduction: int = 16
    
    def setup(self):
        pass
    
    @nn.compact
    def __call__(self, x):
        """
        Args:
            x: (B, H, W, C)
        Returns:
            SE-weighted features
        """
        B, H, W, C = x.shape
        
        # Global Average Pooling
        gap = jnp.mean(x, axis=(1, 2), keepdims=True)  # (B, 1, 1, C)
        
        # Squeeze
        reduced_dim = max(1, C // self.reduction)
        squeeze = nn.Dense(reduced_dim, use_bias=False)(gap)
        squeeze = nn.relu(squeeze)
        
        # Excitation
        excite = nn.Dense(C, use_bias=False)(squeeze)
        excite = nn.sigmoid(excite)
        
        return x * excite


class MBConvBlock(nn.Module):
    """Mobile Inverted Bottleneck Block - EfficientNet核心模块"""
    out_channels: int
    kernel_size: int = 3
    stride: int = 1
    expand_ratio: int = 6
    se_ratio: float = 0.25
    
    @nn.compact
    def __call__(self, x):
        """
        Args:
            x: (B, H, W, C_in)
        Returns:
            (B, H', W', C_out)
        """
        in_channels = x.shape[-1]
        expanded_channels = in_channels * self.expand_ratio
        
        # Expansion phase
        if self.expand_ratio != 1:
            x = nn.Conv(expanded_channels, (1, 1), use_bias=False)(x)
            x = nn.BatchNorm()(x)
            x = nn.swish(x)
        
        # Depthwise convolution
        x = nn.Conv(expanded_channels, (self.kernel_size, self.kernel_size), 
                   strides=(self.stride, self.stride), 
                   feature_group_count=expanded_channels, 
                   padding='SAME', use_bias=False)(x)
        x = nn.BatchNorm()(x)
        x = nn.swish(x)
        
        # SE block
        if self.se_ratio > 0:
            x = SEBlock(reduction=int(1/self.se_ratio))(x)
        
        # Projection phase
        x = nn.Conv(self.out_channels, (1, 1), use_bias=False)(x)
        x = nn.BatchNorm()(x)
        
        # Skip connection
        if self.stride == 1 and in_channels == self.out_channels:
            x = x + x  # Residual connection
        
        return x


class EfficientNetEncoder(nn.Module):
    """EfficientNet-B0 Encoder - SOTA轻量级backbone"""
    
    @nn.compact
    def __call__(self, x):
        """
        Args:
            x: (B, H, W, 3) RGB images
        Returns:
            Multi-scale features dict
        """
        # Stem
        x = nn.Conv(32, (3, 3), strides=(2, 2), padding='SAME', use_bias=False)(x)
        x = nn.BatchNorm()(x)
        x = nn.swish(x)
        
        # Stage 1: MBConv1, k3x3, s1, e1, c16, se0.25
        x = MBConvBlock(16, kernel_size=3, stride=1, expand_ratio=1)(x)
        feat1 = x  # 1/2 scale
        
        # Stage 2: MBConv6, k3x3, s2, e6, c24, se0.25
        x = MBConvBlock(24, kernel_size=3, stride=2, expand_ratio=6)(x)
        x = MBConvBlock(24, kernel_size=3, stride=1, expand_ratio=6)(x)
        feat2 = x  # 1/4 scale
        
        # Stage 3: MBConv6, k5x5, s2, e6, c40, se0.25
        x = MBConvBlock(40, kernel_size=5, stride=2, expand_ratio=6)(x)
        x = MBConvBlock(40, kernel_size=5, stride=1, expand_ratio=6)(x)
        feat3 = x  # 1/8 scale
        
        # Stage 4: MBConv6, k3x3, s2, e6, c80, se0.25
        x = MBConvBlock(80, kernel_size=3, stride=2, expand_ratio=6)(x)
        x = MBConvBlock(80, kernel_size=3, stride=1, expand_ratio=6)(x)
        x = MBConvBlock(80, kernel_size=3, stride=1, expand_ratio=6)(x)
        feat4 = x  # 1/16 scale
        
        # Stage 5: MBConv6, k5x5, s1, e6, c112, se0.25
        x = MBConvBlock(112, kernel_size=5, stride=1, expand_ratio=6)(x)
        x = MBConvBlock(112, kernel_size=5, stride=1, expand_ratio=6)(x)
        x = MBConvBlock(112, kernel_size=5, stride=1, expand_ratio=6)(x)
        feat5 = x  # 1/16 scale
        
        return {
            'feat1': feat1,  # 1/2, 16 channels
            'feat2': feat2,  # 1/4, 24 channels  
            'feat3': feat3,  # 1/8, 40 channels
            'feat4': feat4,  # 1/16, 80 channels
            'feat5': feat5,  # 1/16, 112 channels
        }


class ASPPModule(nn.Module):
    """Atrous Spatial Pyramid Pooling - DeepLabV3+核心模块"""
    out_channels: int = 256
    atrous_rates: Tuple[int, ...] = (6, 12, 18)
    
    @nn.compact
    def __call__(self, x):
        """
        Args:
            x: (B, H, W, C)
        Returns:
            ASPP features (B, H, W, out_channels)
        """
        # 1x1 conv
        conv1x1 = nn.Conv(self.out_channels, (1, 1), use_bias=False)(x)
        conv1x1 = nn.BatchNorm()(conv1x1)
        conv1x1 = nn.relu(conv1x1)
        
        # Atrous convolutions
        atrous_convs = []
        for rate in self.atrous_rates:
            conv = nn.Conv(self.out_channels, (3, 3), 
                          kernel_dilation=(rate, rate),
                          padding='SAME', use_bias=False)(x)
            conv = nn.BatchNorm()(conv)
            conv = nn.relu(conv)
            atrous_convs.append(conv)
        
        # Global average pooling
        gap = jnp.mean(x, axis=(1, 2), keepdims=True)
        gap = nn.Conv(self.out_channels, (1, 1), use_bias=False)(gap)
        gap = nn.BatchNorm()(gap)
        gap = nn.relu(gap)
        gap = jax.image.resize(gap, x.shape, method='bilinear')
        
        # Concatenate all features
        concat_features = jnp.concatenate([conv1x1] + atrous_convs + [gap], axis=-1)
        
        # Final 1x1 conv
        output = nn.Conv(self.out_channels, (1, 1), use_bias=False)(concat_features)
        output = nn.BatchNorm()(output)
        output = nn.relu(output)
        
        return output


class FPNDecoder(nn.Module):
    """Feature Pyramid Network Decoder - 多尺度特征融合"""
    feature_dim: int = 256
    
    @nn.compact
    def __call__(self, multi_scale_feats):
        """
        Args:
            multi_scale_feats: Dict of features at different scales
        Returns:
            Fused features
        """
        # Top-down pathway
        feat5 = multi_scale_feats['feat5']  # 1/16
        feat4 = multi_scale_feats['feat4']  # 1/16
        feat3 = multi_scale_feats['feat3']  # 1/8
        feat2 = multi_scale_feats['feat2']  # 1/4
        
        # Lateral connections
        p5 = nn.Conv(self.feature_dim, (1, 1))(feat5)
        p4 = nn.Conv(self.feature_dim, (1, 1))(feat4)
        p3 = nn.Conv(self.feature_dim, (1, 1))(feat3)
        p2 = nn.Conv(self.feature_dim, (1, 1))(feat2)
        
        # Top-down fusion
        p4 = p4 + jax.image.resize(p5, p4.shape, method='bilinear')
        p3 = p3 + jax.image.resize(p4, p3.shape, method='bilinear')
        p2 = p2 + jax.image.resize(p3, p2.shape, method='bilinear')
        
        # Smooth with 3x3 convs
        p4 = nn.Conv(self.feature_dim, (3, 3), padding='SAME')(p4)
        p3 = nn.Conv(self.feature_dim, (3, 3), padding='SAME')(p3)
        p2 = nn.Conv(self.feature_dim, (3, 3), padding='SAME')(p2)
        
        return {
            'p2': p2,  # 1/4 scale
            'p3': p3,  # 1/8 scale  
            'p4': p4,  # 1/16 scale
            'p5': p5,  # 1/16 scale
        }


class TaskAwareCrossAttention(nn.Module):
    """任务感知交叉注意力机制"""
    embed_dim: int = 256
    num_heads: int = 8
    
    @nn.compact
    def __call__(self, visual_feat, task_context):
        """
        Args:
            visual_feat: (B, H, W, C) 视觉特征
            task_context: (B, D) 任务上下文 (速度、转向、奖励等)
        Returns:
            Task-aware visual features
        """
        B, H, W, C = visual_feat.shape
        
        # Reshape visual features for attention
        visual_tokens = visual_feat.reshape(B, H*W, C)  # (B, N, C)
        
        # Project task context to query
        task_query = nn.Dense(self.embed_dim)(task_context)  # (B, embed_dim)
        task_query = task_query[:, None, :]  # (B, 1, embed_dim)
        
        # Multi-head cross attention
        attended_feat = nn.MultiHeadDotProductAttention(
            num_heads=self.num_heads,
            qkv_features=self.embed_dim
        )(task_query, visual_tokens)  # (B, 1, embed_dim)
        
        # Broadcast back to spatial dimensions
        attended_feat = attended_feat.repeat(H*W, axis=1)  # (B, H*W, embed_dim)
        attended_feat = attended_feat.reshape(B, H, W, self.embed_dim)
        
        # Residual connection
        if C == self.embed_dim:
            output = visual_feat + attended_feat
        else:
            visual_proj = nn.Conv(self.embed_dim, (1, 1))(visual_feat)
            output = visual_proj + attended_feat
        
        return output


class SOTATaskAwareSegNet(nn.Module):
    """
    SOTA任务感知分割网络

    架构特点：
    1. EfficientNet-B0 作为轻量级backbone
    2. FPN多尺度特征融合
    3. ASPP空洞卷积增强感受野
    4. 任务感知交叉注意力
    5. 二分类可通行性输出
    """
    feature_dim: int = 256
    num_classes: int = 1  # 二分类：可通行性
    task_context_dim: int = 64  # 任务上下文维度

    def setup(self):
        self.encoder = EfficientNetEncoder()
        self.fpn = FPNDecoder(feature_dim=self.feature_dim)
        self.aspp = ASPPModule(out_channels=self.feature_dim)
        self.task_attention = TaskAwareCrossAttention(
            embed_dim=self.feature_dim,
            num_heads=8
        )

    @nn.compact
    def __call__(self, rgb_input, task_context=None, training=True):
        """
        前向传播
        Args:
            rgb_input: (B, H, W, 3) RGB图像
            task_context: (B, task_context_dim) 任务上下文
            training: 是否训练模式
        Returns:
            Dict包含分割结果和特征
        """
        B, H, W, _ = rgb_input.shape

        # 1. Encoder: 多尺度特征提取
        multi_scale_feats = self.encoder(rgb_input)

        # 2. FPN: 特征金字塔融合
        fpn_feats = self.fpn(multi_scale_feats)

        # 3. ASPP: 空洞卷积增强感受野
        main_feat = fpn_feats['p3']  # 使用1/8尺度作为主特征
        aspp_feat = self.aspp(main_feat)

        # 4. 任务感知注意力
        if task_context is not None:
            task_aware_feat = self.task_attention(aspp_feat, task_context)
        else:
            task_aware_feat = aspp_feat

        # 5. 分割头：上采样到1/4尺度
        seg_feat = jax.image.resize(
            task_aware_feat,
            (B, H//4, W//4, self.feature_dim),
            method='bilinear'
        )

        # 6. 最终分割输出
        seg_logits = nn.Conv(self.num_classes, (3, 3), padding='SAME')(seg_feat)
        seg_prob = nn.sigmoid(seg_logits)  # 可通行性概率

        # 7. 全局特征用于任务感知评分
        global_feat = jnp.mean(task_aware_feat, axis=(1, 2))  # (B, feature_dim)
        task_score = nn.Dense(1)(global_feat).squeeze(-1)  # (B,)

        # 8. 特征嵌入用于DreamerV3
        embed_feat = nn.Dense(128)(global_feat)  # (B, 128)

        return {
            'passability_prob': seg_prob,  # (B, H/4, W/4, 1)
            'passability_logits': seg_logits,  # (B, H/4, W/4, 1)
            'task_score': task_score,  # (B,)
            'embed_feat': embed_feat,  # (B, 128) 用于DreamerV3
            'global_feat': global_feat,  # (B, 256)
            'multi_scale_feats': fpn_feats,  # 多尺度特征
        }
