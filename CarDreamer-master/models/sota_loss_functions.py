"""
SOTA任务感知损失函数
结合最新的深度学习和强化学习损失设计
"""

import jax
import jax.numpy as jnp
import flax.linen as nn
from typing import Dict, List, Tuple, Optional, Any
import numpy as np

f32 = jnp.float32


class SOTATaskAwareLoss(nn.Module):
    """
    SOTA任务感知损失函数
    
    组合多种先进的损失函数：
    1. Focal Loss + Dice Loss (分割)
    2. Ranking Loss (任务感知)
    3. Contrastive Learning (表征学习)
    4. Temporal Consistency (时序一致性)
    5. Uncertainty-aware Loss (不确定性感知)
    """
    
    def setup(self):
        # 损失权重 - 可学习的权重
        self.loss_weights = self.param(
            'loss_weights',
            nn.initializers.constant(jnp.array([2.0, 1.5, 0.8, 0.5, 0.3])),
            (5,)
        )
        
        # 温度参数
        self.temperature = self.param(
            'temperature',
            nn.initializers.constant(0.1),
            ()
        )
    
    def focal_dice_loss(self, pred_logits, pred_prob, targets, confidence=None):
        """
        Focal Loss + Dice Loss 组合
        处理类别不平衡和边界精确性
        """
        # Focal Loss
        alpha, gamma = 0.25, 2.0
        bce_loss = -(targets * jnp.log(pred_prob + 1e-8) + 
                    (1 - targets) * jnp.log(1 - pred_prob + 1e-8))
        
        pt = jnp.where(targets == 1, pred_prob, 1 - pred_prob)
        focal_loss = alpha * (1 - pt) ** gamma * bce_loss
        
        # Dice Loss
        intersection = jnp.sum(pred_prob * targets, axis=(1,2,3))
        union = jnp.sum(pred_prob, axis=(1,2,3)) + jnp.sum(targets, axis=(1,2,3))
        dice_loss = 1 - (2 * intersection + 1e-8) / (union + 1e-8)
        
        # 置信度加权
        if confidence is not None:
            focal_loss = focal_loss * confidence
            dice_loss = dice_loss * jnp.mean(confidence, axis=(1,2,3))
        
        return jnp.mean(focal_loss) + jnp.mean(dice_loss)
    
    def task_aware_ranking_loss(self, task_scores, rewards, actions, embed_feats):
        """
        任务感知排序损失
        确保分割评分与任务性能正相关
        """
        batch_size = task_scores.shape[0]
        
        # 1. 直接回归损失
        regression_loss = jnp.mean((task_scores - rewards) ** 2)
        
        # 2. 排序损失
        if batch_size > 1:
            # 计算所有pair的排序关系
            score_diff = task_scores[:, None] - task_scores[None, :]  # (B, B)
            reward_diff = rewards[:, None] - rewards[None, :]        # (B, B)
            
            # Hinge loss for ranking
            ranking_loss = jnp.maximum(
                0, 1.0 - score_diff * jnp.sign(reward_diff + 1e-8)
            )
            ranking_loss = jnp.mean(ranking_loss)
        else:
            ranking_loss = 0.0
        
        # 3. 动作一致性损失
        action_consistency = self._compute_action_consistency_loss(
            task_scores, actions, embed_feats
        )
        
        return regression_loss + 0.5 * ranking_loss + 0.3 * action_consistency
    
    def _compute_action_consistency_loss(self, task_scores, actions, embed_feats):
        """计算动作一致性损失"""
        # 解析动作
        throttle, steer, brake = actions[..., 0], actions[..., 1], actions[..., 2]
        
        # 控制质量指标
        # 1. 平滑性：避免急剧变化
        control_smoothness = -(jnp.abs(steer) + 0.5 * jnp.abs(throttle - 0.3))
        
        # 2. 安全性：制动优于加速
        safety_score = brake - 0.5 * throttle
        
        # 3. 效率性：适度的油门控制
        efficiency_score = 1.0 - jnp.abs(throttle - 0.4)
        
        # 综合控制质量
        control_quality = (
            0.4 * control_smoothness + 
            0.4 * safety_score + 
            0.2 * efficiency_score
        )
        
        # 任务评分应与控制质量正相关
        consistency_loss = jnp.mean((task_scores - control_quality) ** 2)
        
        return consistency_loss
    
    def contrastive_learning_loss(self, embed_feats, rewards, task_scores):
        """
        对比学习损失
        相似任务性能的特征应该相似
        """
        batch_size = embed_feats.shape[0]
        if batch_size < 2:
            return 0.0
        
        # 特征归一化
        embed_norm = embed_feats / (jnp.linalg.norm(embed_feats, axis=1, keepdims=True) + 1e-8)
        
        # 计算特征相似度矩阵
        sim_matrix = jnp.dot(embed_norm, embed_norm.T) / self.temperature
        
        # 基于奖励和任务评分的相似度
        reward_sim = jnp.exp(-jnp.abs(rewards[:, None] - rewards[None, :]))
        score_sim = jnp.exp(-jnp.abs(task_scores[:, None] - task_scores[None, :]))
        
        # 组合相似度
        target_sim = 0.6 * reward_sim + 0.4 * score_sim
        
        # InfoNCE-style contrastive loss
        # 正样本：相似度高的pair
        # 负样本：相似度低的pair
        pos_mask = (target_sim > 0.7).astype(f32)
        neg_mask = (target_sim < 0.3).astype(f32)
        
        # 正样本损失：拉近相似的特征
        pos_loss = -jnp.sum(pos_mask * sim_matrix) / (jnp.sum(pos_mask) + 1e-8)
        
        # 负样本损失：推远不相似的特征
        neg_loss = jnp.sum(neg_mask * jnp.exp(sim_matrix)) / (jnp.sum(neg_mask) + 1e-8)
        
        contrastive_loss = pos_loss + jnp.log(neg_loss + 1e-8)
        
        return contrastive_loss
    
    def temporal_consistency_loss(self, current_feats, previous_feats, 
                                 current_probs, previous_probs):
        """
        时序一致性损失
        相邻帧的特征和预测应该相似
        """
        if previous_feats is None or previous_probs is None:
            return 0.0
        
        # 特征一致性
        feat_consistency = jnp.mean((current_feats - previous_feats) ** 2)
        
        # 预测一致性（考虑光流补偿，这里简化）
        prob_consistency = jnp.mean((current_probs - previous_probs) ** 2)
        
        return feat_consistency + prob_consistency
    
    def uncertainty_aware_loss(self, pred_prob, targets, model_uncertainty=None):
        """
        不确定性感知损失
        根据模型不确定性调整损失权重
        """
        # 基础BCE损失
        bce_loss = -(targets * jnp.log(pred_prob + 1e-8) + 
                    (1 - targets) * jnp.log(1 - pred_prob + 1e-8))
        
        if model_uncertainty is not None:
            # 高不确定性区域降低损失权重
            uncertainty_weight = 1.0 / (1.0 + model_uncertainty)
            weighted_loss = bce_loss * uncertainty_weight
        else:
            # 基于预测熵估计不确定性
            pred_entropy = -(pred_prob * jnp.log(pred_prob + 1e-8) + 
                           (1 - pred_prob) * jnp.log(1 - pred_prob + 1e-8))
            
            # 高熵（高不确定性）区域降低权重
            entropy_weight = jnp.exp(-pred_entropy)
            weighted_loss = bce_loss * entropy_weight
        
        return jnp.mean(weighted_loss)
    
    def compute_total_loss(self, 
                          pred_logits,
                          pred_prob, 
                          pseudo_labels,
                          confidence,
                          task_scores,
                          rewards,
                          actions,
                          embed_feats,
                          previous_feats=None,
                          previous_probs=None,
                          model_uncertainty=None):
        """
        计算总损失
        """
        losses = {}
        
        # 1. 分割损失 (Focal + Dice)
        losses['segmentation'] = self.focal_dice_loss(
            pred_logits, pred_prob, pseudo_labels, confidence
        )
        
        # 2. 任务感知损失 (Ranking + Action Consistency)
        losses['task_aware'] = self.task_aware_ranking_loss(
            task_scores, rewards, actions, embed_feats
        )
        
        # 3. 对比学习损失
        losses['contrastive'] = self.contrastive_learning_loss(
            embed_feats, rewards, task_scores
        )
        
        # 4. 时序一致性损失
        losses['temporal'] = self.temporal_consistency_loss(
            embed_feats, previous_feats, pred_prob, previous_probs
        )
        
        # 5. 不确定性感知损失
        losses['uncertainty'] = self.uncertainty_aware_loss(
            pred_prob, pseudo_labels, model_uncertainty
        )
        
        # 使用可学习权重组合损失
        loss_values = jnp.array([
            losses['segmentation'],
            losses['task_aware'], 
            losses['contrastive'],
            losses['temporal'],
            losses['uncertainty']
        ])
        
        # Softmax归一化权重
        normalized_weights = nn.softmax(self.loss_weights)
        total_loss = jnp.sum(normalized_weights * loss_values)
        
        losses['total'] = total_loss
        losses['weights'] = normalized_weights
        
        return total_loss, losses


class AdversarialTaskAwareLoss(nn.Module):
    """
    对抗式任务感知损失
    通过对抗训练提高分割网络的鲁棒性
    """
    
    def setup(self):
        # 判别器：区分真实标签和伪标签
        self.discriminator = nn.Sequential([
            nn.Conv(64, (3, 3), padding='SAME'),
            nn.relu,
            nn.Conv(32, (3, 3), strides=(2, 2), padding='SAME'),
            nn.relu,
            nn.Conv(16, (3, 3), strides=(2, 2), padding='SAME'),
            nn.relu,
            nn.Dense(1),
            nn.sigmoid
        ])
    
    @nn.compact
    def __call__(self, pred_prob, pseudo_labels, real_labels=None):
        """
        对抗损失计算
        Args:
            pred_prob: 模型预测概率
            pseudo_labels: 伪标签
            real_labels: 真实标签（如果有的话）
        """
        # 判别器损失：区分真实标签和预测结果
        if real_labels is not None:
            # 有真实标签的情况
            real_score = self.discriminator(real_labels)
            fake_score = self.discriminator(pred_prob)
            
            # 判别器损失
            d_loss_real = -jnp.mean(jnp.log(real_score + 1e-8))
            d_loss_fake = -jnp.mean(jnp.log(1 - fake_score + 1e-8))
            discriminator_loss = d_loss_real + d_loss_fake
            
            # 生成器损失（分割网络）
            generator_loss = -jnp.mean(jnp.log(fake_score + 1e-8))
        else:
            # 只有伪标签的情况
            pseudo_score = self.discriminator(pseudo_labels)
            pred_score = self.discriminator(pred_prob)
            
            # 判别器损失：伪标签为真，预测为假
            d_loss_pseudo = -jnp.mean(jnp.log(pseudo_score + 1e-8))
            d_loss_pred = -jnp.mean(jnp.log(1 - pred_score + 1e-8))
            discriminator_loss = d_loss_pseudo + d_loss_pred
            
            # 生成器损失
            generator_loss = -jnp.mean(jnp.log(pred_score + 1e-8))
        
        return {
            'discriminator_loss': discriminator_loss,
            'generator_loss': generator_loss,
            'adversarial_loss': 0.1 * generator_loss  # 较小权重的对抗损失
        }
