# 多样化数据训练模型评估报告

模型路径: training_output_diverse/final_diverse_model.pkl
评估时间: 2025-08-20T02:46:29

## 总体性能

- 平均相关性: 0.109 ± 0.149
- 平均分类准确率: 0.576 ± 0.066
- 评估数据类型: 6

## 各数据类型详细结果

### Expert Navigation

- 样本数: 100
- 相关性: 0.340
- 分类准确率: 0.620
- 预测评分: 0.901 ± 0.107
- 真实奖励: 2.839 ± 1.085

### Expert Four Lane

- 样本数: 60
- 相关性: -0.013
- 分类准确率: 0.533
- 预测评分: 0.937 ± 0.092
- 真实奖励: 2.438 ± 1.152

### Cross Navigation→Four Lane

- 样本数: 40
- 相关性: 0.029
- 分类准确率: 0.600
- 预测评分: 0.927 ± 0.169
- 真实奖励: 1.520 ± 2.175

### Cross Four Lane→Navigation

- 样本数: 40
- 相关性: -0.062
- 分类准确率: 0.450
- 预测评分: 0.908 ± 0.075
- 真实奖励: 2.368 ± 1.123

### Noisy High Navigation

- 样本数: 40
- 相关性: 0.277
- 分类准确率: 0.650
- 预测评分: 0.985 ± 0.027
- 真实奖励: 2.966 ± 1.181

### Noisy Med Four Lane

- 样本数: 40
- 相关性: 0.085
- 分类准确率: 0.600
- 预测评分: 0.933 ± 0.098
- 真实奖励: 2.631 ± 1.185

## 性能分析

**最佳相关性**: Expert Navigation (0.340)
**最差相关性**: Cross Four Lane→Navigation (-0.062)

**Expert数据平均相关性**: 0.163
**Cross-Task数据平均相关性**: -0.017
**Noisy数据平均相关性**: 0.181
