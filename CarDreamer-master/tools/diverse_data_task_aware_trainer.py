"""
多样化数据任务感知分割训练器
专门针对收集的多样化数据进行训练

数据类型：
1. 正样本：expert_* - 使用匹配checkpoint在对应任务中采集的标准数据
2. 失败样本：cross_* - 任务和checkpoint不匹配的失败样本
3. 噪声样本：noisy_* - 在动作中引入噪声的样本
4. 困难样本：hard_* - 复杂场景如left-hard、traffic_light等
"""

import os
import sys
import pickle
import time
import numpy as np
import jax
import jax.numpy as jnp
from jax import random
import optax
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import matplotlib.pyplot as plt

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
from models.sota_task_aware_seg import SOTATaskAwareSegNet
from dreamerv3 import ninjax as nj

f32 = jnp.float32


class DiverseDataTaskAwareTrainer:
    """多样化数据任务感知分割训练器"""
    
    def __init__(self, config):
        self.config = config
        self.data_dir = Path(config.get('data_dir', 'data/episodes_diverse'))
        self.output_dir = Path(config.get('output_dir', 'training_output_diverse'))
        self.output_dir.mkdir(exist_ok=True)
        
        # 模型配置
        self.feature_dim = config.get('feature_dim', 256)
        self.task_context_dim = config.get('task_context_dim', 64)
        
        # 训练配置
        self.batch_size = config.get('batch_size', 8)
        self.learning_rate = config.get('learning_rate', 1e-4)
        self.num_epochs = config.get('num_epochs', 50)
        
        # 初始化模型和优化器
        self.setup_model()
        self.setup_optimizer()
        
        # 数据统计
        self.data_stats = {}
        
        print(f"🚀 多样化数据训练器初始化完成")
        print(f"   数据目录: {self.data_dir}")
        print(f"   输出目录: {self.output_dir}")
    
    def setup_model(self):
        """初始化模型"""
        self.model = SOTATaskAwareSegNet(
            feature_dim=self.feature_dim,
            task_context_dim=self.task_context_dim
        )
        
        # 初始化参数
        rng = random.PRNGKey(42)
        dummy_rgb = jnp.ones((1, 224, 224, 3))
        dummy_context = jnp.ones((1, self.task_context_dim))
        
        self.params = self.model.init(rng, dummy_rgb, dummy_context, training=True)
        
        print("✅ 模型初始化完成")
    
    def setup_optimizer(self):
        """设置优化器"""
        # 使用AdamW优化器
        self.optimizer = optax.adamw(
            learning_rate=self.learning_rate,
            weight_decay=1e-4
        )
        self.opt_state = self.optimizer.init(self.params)
        
        print("✅ 优化器初始化完成")
    
    def load_episode_data(self, episode_path: Path) -> Dict[str, np.ndarray]:
        """加载单个episode数据"""
        try:
            # 加载观察数据
            obs_files = list(episode_path.glob('obs_*.pkl'))
            if not obs_files:
                return None
            
            # 加载第一个观察文件作为示例
            with open(obs_files[0], 'rb') as f:
                obs_data = pickle.load(f)
            
            # 提取RGB图像
            if 'camera' in obs_data:
                rgb_image = obs_data['camera']
            elif 'image' in obs_data:
                rgb_image = obs_data['image']
            else:
                print(f"⚠️  未找到图像数据: {episode_path}")
                return None
            
            # 加载动作和奖励数据
            action_files = list(episode_path.glob('action_*.pkl'))
            reward_files = list(episode_path.glob('reward_*.pkl'))
            
            actions = []
            rewards = []
            
            for i in range(len(obs_files)):
                # 动作
                action_file = episode_path / f'action_{i:06d}.pkl'
                if action_file.exists():
                    with open(action_file, 'rb') as f:
                        action = pickle.load(f)
                        actions.append(action)
                
                # 奖励
                reward_file = episode_path / f'reward_{i:06d}.pkl'
                if reward_file.exists():
                    with open(reward_file, 'rb') as f:
                        reward = pickle.load(f)
                        rewards.append(reward)
            
            return {
                'rgb_images': [rgb_image],  # 简化版本，只取第一帧
                'actions': actions[:1] if actions else [np.zeros(3)],
                'rewards': rewards[:1] if rewards else [0.0],
                'episode_path': str(episode_path)
            }
            
        except Exception as e:
            print(f"❌ 加载episode数据失败 {episode_path}: {e}")
            return None
    
    def analyze_data_distribution(self):
        """分析数据分布"""
        print("📊 分析数据分布...")
        
        data_types = {
            'expert': [],      # 正样本
            'cross': [],       # 失败样本  
            'noisy': [],       # 噪声样本
            'hard': []         # 困难样本
        }
        
        # 遍历数据目录
        for data_folder in self.data_dir.iterdir():
            if not data_folder.is_dir():
                continue
                
            folder_name = data_folder.name
            
            # 分类数据类型
            if folder_name.startswith('expert_'):
                data_type = 'expert'
            elif folder_name.startswith('cross_'):
                data_type = 'cross'
            elif folder_name.startswith('noisy_'):
                data_type = 'noisy'
            elif folder_name.startswith('hard_'):
                data_type = 'hard'
            else:
                continue
            
            # 统计episode数量
            episodes = list(data_folder.glob('ep_*'))
            data_types[data_type].extend(episodes)
        
        # 输出统计信息
        for data_type, episodes in data_types.items():
            print(f"   {data_type:>8}: {len(episodes):>3} episodes")
        
        self.data_stats = data_types
        return data_types
    
    def create_training_batches(self, data_types: Dict[str, List]) -> List[Dict]:
        """创建训练批次"""
        print("🔄 创建训练批次...")
        
        all_batches = []
        
        # 为每种数据类型创建批次
        for data_type, episodes in data_types.items():
            print(f"   处理 {data_type} 数据: {len(episodes)} episodes")
            
            type_batches = []
            current_batch = {
                'rgb_images': [],
                'actions': [],
                'rewards': [],
                'data_type': data_type,
                'episode_paths': []
            }
            
            for episode_path in episodes:
                episode_data = self.load_episode_data(episode_path)
                if episode_data is None:
                    continue
                
                # 添加到当前批次
                current_batch['rgb_images'].extend(episode_data['rgb_images'])
                current_batch['actions'].extend(episode_data['actions'])
                current_batch['rewards'].extend(episode_data['rewards'])
                current_batch['episode_paths'].append(episode_data['episode_path'])
                
                # 如果批次满了，保存并创建新批次
                if len(current_batch['rgb_images']) >= self.batch_size:
                    type_batches.append(current_batch)
                    current_batch = {
                        'rgb_images': [],
                        'actions': [],
                        'rewards': [],
                        'data_type': data_type,
                        'episode_paths': []
                    }
            
            # 添加最后一个批次（如果不为空）
            if current_batch['rgb_images']:
                type_batches.append(current_batch)
            
            all_batches.extend(type_batches)
            print(f"     创建了 {len(type_batches)} 个批次")
        
        print(f"✅ 总共创建了 {len(all_batches)} 个训练批次")
        return all_batches
    
    def generate_pseudo_labels(self, batch: Dict) -> jnp.ndarray:
        """基于数据类型生成伪标签"""
        data_type = batch['data_type']
        batch_size = len(batch['rgb_images'])
        
        # 根据数据类型生成不同的伪标签
        if data_type == 'expert':
            # 正样本：高可通行性
            base_prob = 0.8
            noise_level = 0.1
        elif data_type == 'cross':
            # 失败样本：低可通行性
            base_prob = 0.3
            noise_level = 0.2
        elif data_type == 'noisy':
            # 噪声样本：中等可通行性，高不确定性
            base_prob = 0.5
            noise_level = 0.3
        elif data_type == 'hard':
            # 困难样本：低到中等可通行性
            base_prob = 0.4
            noise_level = 0.2
        else:
            base_prob = 0.5
            noise_level = 0.2
        
        # 生成伪标签 (简化版本，实际应该基于图像内容)
        pseudo_labels = []
        for i in range(batch_size):
            # 创建基础标签
            label = np.full((56, 56, 1), base_prob, dtype=np.float32)  # 1/4 scale
            
            # 添加噪声
            noise = np.random.normal(0, noise_level, label.shape).astype(np.float32)
            label = np.clip(label + noise, 0.0, 1.0)
            
            pseudo_labels.append(label)
        
        return jnp.array(pseudo_labels)
    
    def compute_task_aware_loss(self, pred_outputs, pseudo_labels, batch):
        """计算任务感知损失"""
        pred_prob = pred_outputs['passability_prob']
        task_scores = pred_outputs['task_score']
        
        # 1. 分割损失 (BCE + Dice)
        bce_loss = -jnp.mean(
            pseudo_labels * jnp.log(pred_prob + 1e-8) + 
            (1 - pseudo_labels) * jnp.log(1 - pred_prob + 1e-8)
        )
        
        # Dice loss
        intersection = jnp.sum(pred_prob * pseudo_labels, axis=(1,2,3))
        union = jnp.sum(pred_prob, axis=(1,2,3)) + jnp.sum(pseudo_labels, axis=(1,2,3))
        dice_loss = 1 - jnp.mean((2 * intersection + 1e-8) / (union + 1e-8))
        
        seg_loss = bce_loss + dice_loss
        
        # 2. 任务感知损失
        rewards = jnp.array(batch['rewards'], dtype=jnp.float32)
        
        # 任务评分应该与奖励相关
        task_loss = jnp.mean((task_scores - rewards) ** 2)
        
        # 3. 数据类型特定损失
        data_type = batch['data_type']
        if data_type == 'expert':
            # 正样本：鼓励高评分
            type_loss = jnp.mean(jnp.maximum(0, 0.5 - task_scores))
        elif data_type == 'cross':
            # 失败样本：鼓励低评分
            type_loss = jnp.mean(jnp.maximum(0, task_scores - (-0.5)))
        else:
            type_loss = 0.0
        
        total_loss = seg_loss + 0.5 * task_loss + 0.3 * type_loss
        
        return total_loss, {
            'seg_loss': seg_loss,
            'task_loss': task_loss,
            'type_loss': type_loss,
            'total_loss': total_loss
        }
    
    def train_step(self, params, opt_state, batch):
        """单步训练"""
        def loss_fn(params):
            # 准备输入
            rgb_images = jnp.array(batch['rgb_images'])
            task_context = jnp.zeros((len(batch['rgb_images']), self.task_context_dim))
            
            # 前向传播
            pred_outputs = self.model.apply(
                params, rgb_images, task_context, training=True,
                rngs={'dropout': random.PRNGKey(int(time.time()))}
            )
            
            # 生成伪标签
            pseudo_labels = self.generate_pseudo_labels(batch)
            
            # 计算损失
            total_loss, loss_dict = self.compute_task_aware_loss(
                pred_outputs, pseudo_labels, batch
            )
            
            return total_loss, (pred_outputs, loss_dict)
        
        # 计算梯度
        (loss, (pred_outputs, loss_dict)), grads = jax.value_and_grad(
            loss_fn, has_aux=True
        )(params)
        
        # 更新参数
        updates, new_opt_state = self.optimizer.update(grads, opt_state, params)
        new_params = optax.apply_updates(params, updates)
        
        return new_params, new_opt_state, loss, loss_dict, pred_outputs
    
    def train(self):
        """主训练循环"""
        print("🎯 开始训练...")
        
        # 分析数据分布
        data_types = self.analyze_data_distribution()
        
        # 创建训练批次
        training_batches = self.create_training_batches(data_types)
        
        if not training_batches:
            print("❌ 没有找到有效的训练数据")
            return
        
        # 训练历史
        training_history = {
            'losses': [],
            'metrics': [],
            'data_type_performance': {dt: [] for dt in data_types.keys()}
        }
        
        # 编译训练函数
        train_step_jit = jax.jit(self.train_step)
        
        # 训练循环
        step = 0
        for epoch in range(self.num_epochs):
            print(f"\n📅 Epoch {epoch + 1}/{self.num_epochs}")
            
            epoch_losses = []
            
            # 随机打乱批次
            np.random.shuffle(training_batches)
            
            for batch_idx, batch in enumerate(training_batches):
                # 训练步骤
                self.params, self.opt_state, loss, loss_dict, pred_outputs = train_step_jit(
                    self.params, self.opt_state, batch
                )
                
                epoch_losses.append(float(loss))
                
                # 记录不同数据类型的性能
                data_type = batch['data_type']
                avg_task_score = float(jnp.mean(pred_outputs['task_score']))
                training_history['data_type_performance'][data_type].append(avg_task_score)
                
                # 日志输出
                if batch_idx % 10 == 0:
                    print(f"  Batch {batch_idx:3d}/{len(training_batches):3d} | "
                          f"Loss: {loss:.4f} | "
                          f"Type: {data_type:>6} | "
                          f"Score: {avg_task_score:6.3f}")
                
                step += 1
            
            # Epoch总结
            avg_loss = np.mean(epoch_losses)
            training_history['losses'].append(avg_loss)
            
            print(f"  Epoch {epoch + 1} 平均损失: {avg_loss:.4f}")
            
            # 保存检查点
            if (epoch + 1) % 10 == 0:
                self.save_checkpoint(f'checkpoint_epoch_{epoch + 1}.pkl')
        
        # 保存最终模型和训练历史
        self.save_checkpoint('final_diverse_model.pkl')
        self.save_training_history(training_history)
        
        print("🎉 训练完成！")
    
    def save_checkpoint(self, filename):
        """保存检查点"""
        checkpoint = {
            'params': self.params,
            'opt_state': self.opt_state,
            'config': self.config,
            'data_stats': self.data_stats
        }
        
        checkpoint_path = self.output_dir / filename
        with open(checkpoint_path, 'wb') as f:
            pickle.dump(checkpoint, f)
        
        print(f"💾 检查点已保存: {checkpoint_path}")
    
    def save_training_history(self, history):
        """保存训练历史"""
        history_path = self.output_dir / 'training_history.pkl'
        with open(history_path, 'wb') as f:
            pickle.dump(history, f)
        
        # 绘制训练曲线
        self.plot_training_curves(history)
        
        print(f"📈 训练历史已保存: {history_path}")
    
    def plot_training_curves(self, history):
        """绘制训练曲线"""
        fig, axes = plt.subplots(1, 2, figsize=(15, 5))
        
        # 损失曲线
        axes[0].plot(history['losses'])
        axes[0].set_title('Training Loss')
        axes[0].set_xlabel('Epoch')
        axes[0].set_ylabel('Loss')
        axes[0].grid(True)
        
        # 不同数据类型的性能
        for data_type, scores in history['data_type_performance'].items():
            if scores:
                axes[1].plot(scores, label=data_type, alpha=0.7)
        
        axes[1].set_title('Task Score by Data Type')
        axes[1].set_xlabel('Step')
        axes[1].set_ylabel('Task Score')
        axes[1].legend()
        axes[1].grid(True)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'training_curves.png', dpi=150)
        plt.show()


def create_diverse_training_config():
    """创建多样化数据训练配置"""
    return {
        'data_dir': 'data/episodes_diverse',
        'output_dir': 'training_output_diverse',
        'feature_dim': 256,
        'task_context_dim': 64,
        'batch_size': 8,
        'learning_rate': 1e-4,
        'num_epochs': 30,
    }


if __name__ == '__main__':
    config = create_diverse_training_config()
    trainer = DiverseDataTaskAwareTrainer(config)
    trainer.train()
