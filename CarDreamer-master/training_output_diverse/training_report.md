# 多样化数据训练报告

## 训练配置
- 数据目录: /home/<USER>/seg0810/CarDreamer-master/data/episodes_diverse
- 输出目录: /home/<USER>/seg0810/CarDreamer-master/training_output_diverse
- 训练轮数: 50
- 批次大小: 8
- 学习率: 1e-4
- 训练时间: Wed Aug 20 02:46:07 AM UTC 2025

## 数据统计
请查看以下文件获取详细信息:
- `data_validation_report.txt` - 数据验证报告
- `data_summary.png` - 数据分布可视化
- `reward_distributions.png` - 奖励分布图

## 训练结果
请查看以下文件获取训练结果:
- `final_diverse_model.pkl` - 最终训练模型
- `training_history.pkl` - 训练历史数据
- `training_curves.png` - 训练曲线图

## 模型文件
- 最终模型: `final_diverse_model.pkl`
- 检查点: `checkpoint_epoch_*.pkl`

## 使用方法
```python
from tools.task_aware_passability_trainer import TaskAwarePassabilityTrainer

# 加载训练好的模型
trainer = TaskAwarePassabilityTrainer()
trainer.load_model('/home/<USER>/seg0810/CarDreamer-master/training_output_diverse/final_diverse_model.pkl')

# 进行推理
# ... 你的推理代码
```

---
生成时间: Wed Aug 20 02:46:07 AM UTC 2025
